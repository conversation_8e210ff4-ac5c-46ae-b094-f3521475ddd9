package sysmontap

import (
	"fmt"
	"log/slog"
	"os"
	"os/signal"
	"time"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
	"github.com/danielpaulus/go-ios/ios/instruments"
)

type Options struct {
	*options.Options

	BundleID string
	Interval time.Duration
}

// Deprecated: NewSysmontapCmd creates a new sysmontap command
func NewSysmontapCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}
	cmd := &cobra.Command{
		Use:        "sysmontap",
		Short:      "Get system statistics",
		Long:       `Get system stats like CPU, MEMORY.`,
		Deprecated: "Use 'instruments sysmontap' instead.",
		RunE: func(cmd *cobra.Command, args []string) error {
			dev, err := helpers.GetDevice(opts)
			if err != nil {
				return fmt.Errorf("error getting device: %w", err)
			}

			return runSysmontap(dev, o)
		},
	}

	cmd.Flags().StringVar(&o.BundleID, "bundle-id", "", "Bundle ID")
	cmd.Flags().DurationVar(
		&o.Interval, "interval", 0,
		fmt.Sprintf("Output interval, [%s, %s]", instruments.MinOutputInterval, instruments.MaxOutputInterval),
	)
	_ = cmd.MarkFlagRequired("bundle-id")

	return cmd
}

func runSysmontap(device ios.DeviceEntry, opts *Options) error {
	sysmontapOpts := make([]instruments.SysmontapOption, 0, 3)
	if opts.BundleID != "" {
		sysmontapOpts = append(sysmontapOpts, instruments.WithBundleIDOfSysmontap(opts.BundleID))
	}
	if opts.Interval != 0 {
		sysmontapOpts = append(sysmontapOpts, instruments.WithIntervalOfSysmontap(opts.Interval))
	}
	sysmontapOpts = append(sysmontapOpts, instruments.WithCallbackFuncOfSysmontap(output))

	sysmon, err := instruments.NewSysmontapService(device, sysmontapOpts...)
	if err != nil {
		return fmt.Errorf("error creating system monitor: %w", err)
	}
	defer func(sysmon *instruments.SysmontapService) {
		if sysmon != nil {
			_ = sysmon.Close()
		}
	}(sysmon)

	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt)

	slog.Info("Starting to monitor CPU usage... Press CTRL+C to stop.")
	<-c
	slog.Info("Shutting down sysmontap")
	return nil
}

func output(data *instruments.SysmontapData) {
	slog.Info(
		"received sysmontap data",
		"timestamp", data.Timestamp,
		"usage", data.Usage,
		"pss", data.PSS,
		"rss", data.RSS,
		"vss", data.VSS,
		"pid", data.PID,
		"name", data.Name,
	)
}
