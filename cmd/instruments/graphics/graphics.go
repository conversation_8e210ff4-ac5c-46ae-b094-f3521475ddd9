package graphics

import (
	"fmt"
	"log/slog"
	"os"
	"os/signal"
	"time"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/instruments"
)

type Options struct {
	*options.Options

	Interval time.Duration
}

func NewGraphicsCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}
	cmd := &cobra.Command{
		Use:   "graphics",
		Short: "Get graphics statistics",
		Long:  `Get graphics stats like FPS.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runGraphics(o)
		},
	}

	cmd.Flags().DurationVar(
		&o.Interval, "interval", 0,
		fmt.Sprintf("Output interval, [%s, %s]", instruments.MinOutputInterval, instruments.MaxOutputInterval),
	)

	return cmd
}

func runGraphics(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("failed to get device: %w", err)
	}

	graphicsOpts := make([]instruments.GraphicsOption, 0, 2)
	if opts.Interval != 0 {
		graphicsOpts = append(graphicsOpts, instruments.WithIntervalOfGraphics(opts.Interval))
	}

	svc, err := instruments.NewGraphicsService(device, graphicsOpts...)
	if err != nil {
		return fmt.Errorf("failed to create graphics service: %w", err)
	}
	defer func(svc *instruments.GraphicsService) {
		if svc != nil {
			_ = svc.Close()
		}
	}(svc)

	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt)

	slog.Info("Start to get graphics data... Press CTRL+C to stop.")
	<-c
	slog.Info("Stop to get graphics data")
	return nil
}
