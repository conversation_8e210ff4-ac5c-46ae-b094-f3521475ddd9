package sysmontap

import (
	"fmt"
	"log/slog"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/instruments"
)

type Options struct {
	*options.Options

	BundleID string
	Interval time.Duration
}

func NewSysmontapCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}
	cmd := &cobra.Command{
		Use:   "sysmontap",
		Short: "Get system statistics",
		Long:  `Get system stats like MEM, CPU.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runSysmontap(o)
		},
	}

	cmd.Flags().StringVar(&o.BundleID, "bundle-id", "", "Bundle ID")
	cmd.Flags().DurationVar(
		&o.Interval, "interval", 0,
		fmt.Sprintf("Output interval, [%s, %s]", instruments.MinOutputInterval, instruments.MaxOutputInterval),
	)
	_ = cmd.MarkFlagRequired("bundle-id")

	return cmd
}

func runSysmontap(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("failed to get device: %w", err)
	}

	sysmontapOpts := make([]instruments.SysmontapOption, 0, 3)
	if opts.BundleID != "" {
		sysmontapOpts = append(sysmontapOpts, instruments.WithBundleIDOfSysmontap(opts.BundleID))
	}
	if opts.Interval != 0 {
		sysmontapOpts = append(sysmontapOpts, instruments.WithIntervalOfSysmontap(opts.Interval))
	}
	sysmontapOpts = append(sysmontapOpts, instruments.WithCallbackFuncOfSysmontap(output))

	svc, err := instruments.NewSysmontapService(device, sysmontapOpts...)
	if err != nil {
		return fmt.Errorf("failed to create sysmontap service: %w", err)
	}
	defer func(svc *instruments.SysmontapService) {
		if svc != nil {
			_ = svc.Close()
		}
	}(svc)

	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGINT, syscall.SIGTERM)

	slog.Info("Start to get sysmontap data... Press CTRL+C to stop.")
	<-c
	slog.Info("Stop to get sysmontap data")
	return nil
}

func output(data *instruments.SysmontapData) {
	slog.Info(
		"received sysmontap data",
		"timestamp", data.Timestamp,
		"usage", data.Usage,
		"pss", data.PSS,
		"rss", data.RSS,
		"vss", data.VSS,
		"pid", data.PID,
		"name", data.Name,
	)
}
