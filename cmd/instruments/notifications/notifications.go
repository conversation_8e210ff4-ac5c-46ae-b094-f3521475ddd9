package notifications

import (
	"fmt"
	"log/slog"
	"os"
	"os/signal"
	"syscall"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/instruments"
)

func NewNotificationsCmd(opts *options.Options) *cobra.Command {
	return &cobra.Command{
		Use:   "notifications",
		Short: "Listen for application state notifications",
		Long:  `Listen for application state notifications.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runInstruments(opts)
		},
	}
}

func runInstruments(opts *options.Options) error {
	device, err := helpers.GetDevice(opts)
	if err != nil {
		return fmt.Errorf("failed to get device: %w", err)
	}

	listenerFunc, closeFunc, err := instruments.ListenAppStateNotifications(device)
	if err != nil {
		return fmt.Errorf("failed to listen for app state notifications: %w", err)
	}
	defer func() {
		if closeFunc != nil {
			_ = closeFunc()
		}
	}()

	go func() {
		for {
			notification, err := listenerFunc()
			if err != nil {
				slog.Error("got an error while listening for notifications", "error", err)
				return
			}

			slog.Info("received notification", "notification", notification)
		}
	}()

	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGINT, syscall.SIGTERM)

	slog.Info("Start to listen for application state notifications... Press Ctrl+C to stop")
	<-c
	slog.Info("Stop to listen for application state notifications")

	return nil
}
