package instruments

import (
	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/instruments/graphics"
	"github.com/danielpaulus/go-ios/cmd/instruments/notifications"
	"github.com/danielpaulus/go-ios/cmd/instruments/sysmontap"
	"github.com/danielpaulus/go-ios/cmd/options"
)

// NewInstrumentsCmd creates a new instruments command
func NewInstrumentsCmd(opts *options.Options) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "instruments",
		Short: "Access instruments features",
		Long:  `Access instruments features for performance analysis.`,
	}

	// Add subcommands
	cmd.AddCommand(graphics.NewGraphicsCmd(opts))
	cmd.AddCommand(notifications.NewNotificationsCmd(opts))
	cmd.AddCommand(sysmontap.NewSysmontapCmd(opts))

	return cmd
}
