package dtx

import (
	"bytes"
	"encoding/binary"
)

// BuildAckMessage creates a 32+16 byte long message that can be used as a response for a message
func BuildAckMessage(msg *Message) []byte {
	buf := new(bytes.Buffer)

	header := &MessageHeader{
		Magic:             DtxMessageMagic,
		CB:                DtxMessageHeaderLength,
		FragmentID:        0,
		FragmentCount:     1,
		Length:            DtxMessagePayloadHeaderLength,
		Identifier:        msg.Header.Identifier,
		ConversationIndex: msg.Header.ConversationIndex + 1,
		ChannelCode:       msg.Header.ChannelCode,
		ExpectsReply:      0,
	}
	header.Write(buf)

	payloadHeader := &MessagePayloadHeader{
		Flags:           Ack,
		AuxiliaryLength: 0,
		TotalLength:     0,
	}
	_ = binary.Write(buf, binary.LittleEndian, payloadHeader)

	return buf.Bytes()
}

// Encode encodes the given parameters to a DtxMessage that can be sent to the device.
func Encode(
	identifier,
	conversationIndex,
	channelCode int,
	expectsReply bool,
	messageType MessageType,
	payloadBytes []byte,
	auxiliary *PrimitiveDictionary,
) ([]byte, error) {
	var (
		buf           = new(bytes.Buffer)
		payloadLength = uint32(len(payloadBytes))

		auxBytes  []byte
		auxLength uint32
		err       error
	)

	if auxiliary != nil {
		auxBytes, err = auxiliary.ToBytes()
		if err != nil {
			return nil, err
		}
	}
	auxLength = uint32(len(auxBytes))

	header := &MessageHeader{
		Magic:             DtxMessageMagic,
		CB:                DtxMessageHeaderLength,
		FragmentID:        0,
		FragmentCount:     1,
		Length:            DtxMessagePayloadHeaderLength + payloadLength,
		Identifier:        uint32(identifier),
		ConversationIndex: uint32(conversationIndex),
		ChannelCode:       uint32(channelCode),
	}
	payloadHeader := &MessagePayloadHeader{
		Flags: messageType,
	}
	if auxLength > 0 {
		header.Length += DtxAuxiliaryHeaderLength + auxLength
		payloadHeader.AuxiliaryLength = DtxAuxiliaryHeaderLength + auxLength
	}
	if expectsReply {
		header.ExpectsReply = 1
	}
	payloadHeader.TotalLength = uint64(payloadHeader.AuxiliaryLength) + uint64(payloadLength)

	header.Write(buf)                                         // MessageHeader
	_ = binary.Write(buf, binary.LittleEndian, payloadHeader) // MessagePayloadHeader
	if auxLength > 0 {
		auxiliaryHeader := &AuxiliaryHeader{
			BufferSize:    DtxAuxiliaryHeaderBufferSize,
			Unknown:       0,
			AuxiliarySize: auxLength,
			Unknown2:      0,
		}
		_ = binary.Write(buf, binary.LittleEndian, auxiliaryHeader) // AuxiliaryHeader
		buf.Write(auxBytes)                                         // AuxiliaryPayload
	}
	buf.Write(payloadBytes) // Payload

	return buf.Bytes(), nil
}

//func writeHeader(
//	messageBytes []byte, messageLength uint32, Identifier, ConversationIndex int,
//	ChannelCode int,
//	ExpectsReply bool,
//) {
//	binary.LittleEndian.PutUint32(messageBytes, DtxMessageMagic)
//	binary.LittleEndian.PutUint32(messageBytes[4:], DtxMessageHeaderLength)
//	binary.LittleEndian.PutUint16(messageBytes[8:], 0)
//	binary.LittleEndian.PutUint16(messageBytes[10:], 1)
//	binary.LittleEndian.PutUint32(messageBytes[12:], messageLength)
//	binary.LittleEndian.PutUint32(messageBytes[16:], uint32(Identifier))
//	binary.LittleEndian.PutUint32(messageBytes[20:], uint32(ConversationIndex))
//	binary.LittleEndian.PutUint32(messageBytes[24:], uint32(ChannelCode))
//	var expectsReplyUint32 uint32
//	if ExpectsReply {
//		expectsReplyUint32 = 1
//	} else {
//		expectsReplyUint32 = 0
//	}
//	binary.LittleEndian.PutUint32(messageBytes[28:], expectsReplyUint32)
//}
//
//func writePayloadHeader(messageBytes []byte, messageType MessageType, payloadLength, auxLength int) {
//	binary.LittleEndian.PutUint32(messageBytes, uint32(messageType))
//	auxLengthWithHeader := uint32(auxLength)
//	if auxLength > 0 {
//		auxLengthWithHeader += 16
//	}
//	binary.LittleEndian.PutUint32(messageBytes[4:], auxLengthWithHeader)
//	binary.LittleEndian.PutUint32(messageBytes[8:], uint32(payloadLength)+auxLengthWithHeader)
//	binary.LittleEndian.PutUint32(messageBytes[12:], 0)
//}
//
//func writeAuxHeader(messageBytes []byte, auxiliarySize int) {
//	binary.LittleEndian.PutUint32(messageBytes, uint32(496))
//	binary.LittleEndian.PutUint32(messageBytes[4:], 0)
//	binary.LittleEndian.PutUint32(messageBytes[8:], uint32(auxiliarySize))
//	binary.LittleEndian.PutUint32(messageBytes[12:], 0)
//}
