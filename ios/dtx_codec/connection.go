package dtx

import (
	"bufio"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"math"
	"strings"
	"sync"
	"time"

	"github.com/danielpaulus/go-ios/ios"
	"github.com/danielpaulus/go-ios/ios/nskeyedarchiver"
)

type MethodWithResponse func(msg *Message) (any, error)

var ErrConnectionClosed = errors.New("connection closed")

// Connection manages channels, including the GlobalChannel, for a DtxConnection and dispatches received messages
// to the right channel.
type Connection struct {
	deviceConnection       ios.DeviceConnectionInterface
	channelCodeCounter     int
	activeChannels         sync.Map
	globalChannel          *Channel
	capabilities           map[string]any
	mutex                  sync.Mutex
	requestChannelMessages chan *Message

	// MessageDispatcher use this prop to catch messages from GlobalDispatcher
	// and handle it accordingly in a custom dispatcher of the dedicated service
	//
	// Set this prop when creating a connection instance
	//
	// Refer to end-to-end example of `instruments/instruments_sysmontap.go`
	MessageDispatcher Dispatcher

	closed    chan struct{}
	err       error
	closeOnce sync.Once
}

// Dispatcher is a simple interface containing a Dispatch func to receive dtx.Messages
type Dispatcher interface {
	Dispatch(msg *Message)
}

// GlobalDispatcher the message dispatcher for the automatically created global Channel
type GlobalDispatcher struct {
	dispatchFunctions      map[string]func(*Message)
	requestChannelMessages chan *Message
	dtxConnection          *Connection
}

const requestChannel = "_requestChannelWithCode:identifier:"

// Closed is closed when the underlying DTX connection was closed for any reason (either initiated by calling Close() or due to an error)
func (dtxConn *Connection) Closed() <-chan struct{} {
	return dtxConn.closed
}

// Err is non-nil when the connection was closed (when Close was called this will be ErrConnectionClosed)
func (dtxConn *Connection) Err() error {
	return dtxConn.err
}

// Close closes the underlying deviceConnection
func (dtxConn *Connection) Close() error {
	if dtxConn.deviceConnection != nil {
		err := dtxConn.deviceConnection.Close()
		dtxConn.close(err)
		return err
	}
	dtxConn.close(ErrConnectionClosed)
	return nil
}

// GlobalChannel returns the connections automatically created global channel.
func (dtxConn *Connection) GlobalChannel() *Channel {
	return dtxConn.globalChannel
}

// NewGlobalDispatcher create a Dispatcher for the GlobalChannel
func NewGlobalDispatcher(requestChannelMessages chan *Message, dtxConnection *Connection) Dispatcher {
	dispatcher := &GlobalDispatcher{
		dispatchFunctions:      map[string]func(*Message){},
		requestChannelMessages: requestChannelMessages,
		dtxConnection:          dtxConnection,
	}
	const notifyPublishedCaps = "_notifyOfPublishedCapabilities:"
	dispatcher.dispatchFunctions[notifyPublishedCaps] = notifyOfPublishedCapabilities
	return dispatcher
}

// Dispatch to a MessageDispatcher of the Connection if set
func (dtxConn *Connection) Dispatch(msg *Message) {
	msgDispatcher := dtxConn.MessageDispatcher
	if msgDispatcher != nil {
		slog.Debug(fmt.Sprintf("message dispatcher found: %T", msgDispatcher))
		msgDispatcher.Dispatch(msg)
		return
	}

	slog.Error("not found message dispatcher", "msg", msg)
}

// Dispatch prints log messages and errors when they are received and also creates local Channels when requested by the device.
func (d *GlobalDispatcher) Dispatch(msg *Message) {
	if err := SendAckIfNeeded(d.dtxConnection, msg); err != nil {
		slog.Error("failed to send ack for global dispatcher", "err", err)
	}

	if msg.Payload != nil {
		if requestChannel == msg.Payload[0] {
			d.requestChannelMessages <- msg
		}

		// TODO: use the dispatchFunctions map
		if "outputReceived:fromProcess:atTime:" == msg.Payload[0] {
			logMsg, err := nskeyedarchiver.Unarchive(msg.Auxiliary.GetArguments()[0].([]byte))
			if err == nil {
				slog.Debug(
					"receive a `outputReceived:fromProcess:atTime:` message",
					"msg", logMsg[0],
					"pid", msg.Auxiliary.GetArguments()[1],
					"time", msg.Auxiliary.GetArguments()[2],
				)
			}
			return
		}
	}
	slog.Debug("receive a message", "payload", msg.Payload, "auxiliary", msg.Auxiliary)

	if msg.HasError() {
		slog.Error("receive an error message", "payload", msg.Payload[0])
	}

	if msg.PayloadHeader.Flags == UnknownTypeOne || msg.PayloadHeader.Flags == ResponseWithReturnValueInPayload {
		d.dtxConnection.Dispatch(msg)
	}
}

func notifyOfPublishedCapabilities(msg *Message) {
	slog.Debug("published capabilities message", "msg", msg)
}

// NewUsbmuxdConnection connects and starts reading from a Dtx based service on the device
func NewUsbmuxdConnection(device ios.DeviceEntry, serviceName string) (*Connection, error) {
	conn, err := ios.ConnectToService(device, serviceName)
	if err != nil {
		return nil, err
	}

	return newDtxConnection(conn)
}

// NewTunnelConnection connects and starts reading from a Dtx based service on the device, using tunnel interface instead of usbmuxd
func NewTunnelConnection(device ios.DeviceEntry, serviceName string) (*Connection, error) {
	conn, err := ios.ConnectToServiceTunnelIface(device, serviceName)
	if err != nil {
		return nil, err
	}

	return newDtxConnection(conn)
}

func newDtxConnection(conn ios.DeviceConnectionInterface) (*Connection, error) {
	requestChannelMessages := make(chan *Message, 5)

	// The global channel has channelCode 0, so we need to start with channelCodeCounter==1
	dtxConnection := &Connection{
		deviceConnection:       conn,
		channelCodeCounter:     1,
		requestChannelMessages: requestChannelMessages,
	}
	dtxConnection.closed = make(chan struct{})

	// The global channel is automatically present and used for requesting other channels and some other methods like notifyPublishedCapabilities
	dtxConnection.globalChannel = &Channel{
		channelCode:       0,
		messageIdentifier: 5,
		channelName:       "global_channel",
		connection:        dtxConnection,
		messageDispatcher: NewGlobalDispatcher(requestChannelMessages, dtxConnection),
		responseWaiters:   map[int]chan *Message{},
		registeredMethods: map[string]chan *Message{},
		defragmenters:     map[int]*FragmentDecoder{},
		timeout:           5 * time.Second,
	}
	go read(dtxConnection)

	return dtxConnection, nil
}

// Send sends the byte slice directly to the device using the underlying DeviceConnectionInterface
func (dtxConn *Connection) Send(message []byte) error {
	return dtxConn.deviceConnection.Send(message)
}

// read reads messages from the byte stream and dispatches them to the right channel when they are decoded.
func read(dtxConn *Connection) {
	var (
		index int
		msg   *Message
		err   error
	)

	defer func() {
		dtxConn.close(err)
	}()

	r := bufio.NewReader(dtxConn.deviceConnection.Reader())
	for {
		index++
		msg, err = ReadMessage(r)
		if err != nil {
			if err == io.EOF || strings.Contains(err.Error(), "use of closed network") {
				slog.Error("DTX Connection with EOF", "index", index, "error", err)
				return
			}

			slog.Error("error reading dtx connection", "index", index, "error", err)
			return
		}

		slog.Debug("received message", "index", index, "msg", msg)
		if _channel, ok := dtxConn.activeChannels.Load(int(msg.Header.ChannelCode)); ok {
			channel := _channel.(*Channel)
			channel.Dispatch(msg)
		} else {
			dtxConn.globalChannel.Dispatch(msg)
		}
	}
}

func SendAckIfNeeded(dtxConn *Connection, msg *Message) error {
	if msg.Header.ExpectsReply == 1 {
		ack := BuildAckMessage(msg)
		err := dtxConn.Send(ack)
		if err != nil {
			slog.Error("failed to send ack", "err", err)
			return err
		}
	}

	return nil
}

func (dtxConn *Connection) ForChannelRequest(messageDispatcher Dispatcher) *Channel {
	msg := <-dtxConn.requestChannelMessages
	dtxConn.mutex.Lock()
	defer dtxConn.mutex.Unlock()
	// code := msg.Auxiliary.GetArguments()[0].(uint32)
	identifier, _ := nskeyedarchiver.Unarchive(msg.Auxiliary.GetArguments()[1].([]byte))
	// TODO: Setting the channel code here manually to -1 for making testmanagerd work. For some reason it requests the TestDriver proxy channel with code 1 but sends messages on -1. Should probably be fixed somehow
	// TODO: try to refactor testmanagerd/xcuitest code and use AddDefaultChannelReceiver instead of this function. The only code calling this is in testmanagerd right now.
	channel := &Channel{
		channelCode:       -1,
		channelName:       identifier[0].(string),
		messageIdentifier: 1,
		connection:        dtxConn,
		messageDispatcher: messageDispatcher,
		responseWaiters:   map[int]chan *Message{},
		defragmenters:     map[int]*FragmentDecoder{},
		timeout:           5 * time.Second,
	}
	dtxConn.activeChannels.Store(-1, channel)
	return channel
}

// AddDefaultChannelReceiver let's you set the Dispatcher for the Channel with code -1 ( or 4294967295 for uint32).
// I am just calling it the "default" channel now, without actually figuring out what it is for exactly from disassembled code.
// If someone wants to do that and bring some clarity, please go ahead :-)
// This channel seems to always be there without explicitly requesting it and sometimes it is used.
func (dtxConn *Connection) AddDefaultChannelReceiver(messageDispatcher Dispatcher) *Channel {
	channel := &Channel{
		channelCode:       -1,
		channelName:       "c -1/ 4294967295 receiver channel ",
		messageIdentifier: 1,
		connection:        dtxConn,
		messageDispatcher: messageDispatcher,
		responseWaiters:   map[int]chan *Message{},
		defragmenters:     map[int]*FragmentDecoder{},
		timeout:           5 * time.Second,
	}
	dtxConn.activeChannels.Store(math.MaxUint32, channel)
	return channel
}

// RequestChannelIdentifier requests a channel to be opened on the Connection with the given identifier,
// an automatically assigned channelCode and a Dispatcher for receiving messages.
func (dtxConn *Connection) RequestChannelIdentifier(
	identifier string, messageDispatcher Dispatcher, opts ...ChannelOption,
) *Channel {
	dtxConn.mutex.Lock()
	defer dtxConn.mutex.Unlock()
	code := dtxConn.channelCodeCounter
	dtxConn.channelCodeCounter++

	payload, _ := nskeyedarchiver.ArchiveBin(requestChannel)
	auxiliary := NewPrimitiveDictionary()
	auxiliary.AddUInt32(uint32(code))
	arch, _ := nskeyedarchiver.ArchiveBin(identifier)
	auxiliary.AddBytes(arch)
	slog.Debug("requesting channel", "channel_id", identifier, "auxiliary", auxiliary)

	reply, err := dtxConn.globalChannel.SendAndAwaitReply(true, Methodinvocation, payload, auxiliary)
	if err != nil {
		slog.Error("failed to request channel", "channel_id", identifier, "error", err)
	}
	slog.Debug("requested channel", "channel_id", identifier, "reply", reply)

	slog.Debug("channel open", "channel_id", identifier)
	channel := &Channel{
		channelCode:       code,
		channelName:       identifier,
		messageIdentifier: 1,
		connection:        dtxConn,
		messageDispatcher: messageDispatcher,
		responseWaiters:   map[int]chan *Message{},
		defragmenters:     map[int]*FragmentDecoder{},
		timeout:           5 * time.Second,
	}
	dtxConn.activeChannels.Store(code, channel)
	for _, opt := range opts {
		opt(channel)
	}

	return channel
}

func (dtxConn *Connection) close(err error) {
	dtxConn.closeOnce.Do(
		func() {
			dtxConn.err = err
			close(dtxConn.closed)
		},
	)
}
