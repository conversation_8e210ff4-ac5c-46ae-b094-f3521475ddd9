package instruments

import (
	"fmt"
	"log/slog"
	"sync/atomic"
	"time"

	"github.com/go-viper/mapstructure/v2"

	"github.com/danielpaulus/go-ios/ios"
	dtx "github.com/danielpaulus/go-ios/ios/dtx_codec"
)

const (
	serviceNameOfGraphicsOpengl = "com.apple.instruments.server.services.graphics.opengl"

	selectorOfAvailableStatistics         = "availableStatistics"
	selectorOfDriverNames                 = "driverNames"
	selectorOfSetSamplingRate             = "setSamplingRate:"
	selectorOfStartSamplingAtTimeInterval = "startSamplingAtTimeInterval:"
	selectorOfStopSampling                = "stopSampling"
)

var _ dtx.Dispatcher = (*sysmontapMsgDispatcher)(nil)

type graphicsMsgDispatcher struct {
	messageChan chan *dtx.Message
	closed      *atomic.Bool
}

func newGraphicsMsgDispatcher() *graphicsMsgDispatcher {
	return &graphicsMsgDispatcher{
		messageChan: make(chan *dtx.Message),
		closed:      new(atomic.Bool),
	}
}

func (d *graphicsMsgDispatcher) Close() {
	defer func() {
		for range d.messageChan {
		}
	}()

	d.closed.Store(true)
	close(d.messageChan)
}

func (d *graphicsMsgDispatcher) Dispatch(m *dtx.Message) {
	if !d.closed.Load() {
		d.messageChan <- m
	}
}

type (
	GraphicsOption func(*GraphicsService)

	GraphicsService struct {
		dispatcher *graphicsMsgDispatcher
		conn       *dtx.Connection
		channel    *dtx.Channel

		closeChan chan struct{}

		interval time.Duration
	}
)

func WithIntervalOfGraphics(interval time.Duration) GraphicsOption {
	return func(s *GraphicsService) {
		if interval < MinOutputInterval {
			s.interval = MinOutputInterval
		} else if interval > MaxOutputInterval {
			s.interval = MaxOutputInterval
		} else {
			s.interval = interval
		}
	}
}

func NewGraphicsService(device ios.DeviceEntry, opts ...GraphicsOption) (*GraphicsService, error) {
	dispatcher := newGraphicsMsgDispatcher()
	conn, err := connectInstrumentsWithMsgDispatcher(device, dispatcher)
	if err != nil {
		return nil, err
	}
	defer func() {
		if err != nil {
			if conn != nil {
				_ = conn.Close()
			}
			if dispatcher != nil {
				dispatcher.Close()
			}
		}
	}()

	channel := conn.RequestChannelIdentifier(serviceNameOfGraphicsOpengl, loggingDispatcher{conn})
	s := &GraphicsService{
		dispatcher: dispatcher,
		conn:       conn,
		channel:    channel,

		closeChan: make(chan struct{}),

		interval: DefaultOutputInterval,
	}
	for _, opt := range opts {
		opt(s)
	}

	_, err = s.channel.MethodCall(selectorOfAvailableStatistics)
	if err != nil {
		return nil, err
	}

	_, err = s.channel.MethodCall(selectorOfDriverNames)
	if err != nil {
		return nil, err
	}

	_, err = s.channel.MethodCall(selectorOfSetSamplingRate, s.interval.Milliseconds()/100)
	if err != nil {
		return nil, err
	}

	err = s.channel.MethodCallAsync(selectorOfStartSamplingAtTimeInterval, 0.0)
	if err != nil {
		return nil, err
	}

	go s.receiveMessage()
	return s, nil
}

func (s *GraphicsService) Close() error {
	if s == nil {
		return nil
	}

	if s.channel != nil {
		_ = s.channel.MethodCallAsync(selectorOfStopSampling)
	}

	if s.dispatcher != nil {
		s.dispatcher.Close()
	}
	close(s.closeChan)

	if s.conn != nil {
		return s.conn.Close()
	}

	return nil
}

func (s *GraphicsService) receiveMessage() {
	for {
		select {
		case <-s.closeChan:
			return
		case msg, ok := <-s.dispatcher.messageChan:
			if !ok {
				slog.Info("the graphics message channel is closed")
				return
			}

			data, err := s.handleMessage(msg)
			if err != nil {
				slog.Debug("ignore the message", "error", err)
				continue
			}

			slog.Info("receive a message", "data", data)
		}
	}
}

type (
	graphicsPayload struct {
		AllocSystemMemory            uint64 `json:"alloc_system_memory" mapstructure:"Alloc system memory"`
		AllocatedPBSize              uint64 `json:"allocated_pb_size" mapstructure:"Allocated PB Size"`
		CoreAnimationFramesPerSecond uint64 `json:"core_animation_frames_per_second" mapstructure:"CoreAnimationFramesPerSecond"`
		DeviceUtilization            uint64 `json:"device_utilization" mapstructure:"Device Utilization %"`
		IOGLBundleName               string `json:"iogl_bundle_name" mapstructure:"IOGLBundleName"`
		InUseSystemMemory            uint64 `json:"in_use_system_memory" mapstructure:"In use system memory"`
		RendererUtilization          uint64 `json:"renderer_utilization" mapstructure:"Renderer Utilization %"`
		SplitSceneCount              uint64 `json:"split_scene_count" mapstructure:"SplitSceneCount"`
		TiledSceneBytes              uint64 `json:"tiled_scene_bytes" mapstructure:"TiledSceneBytes"`
		TilerUtilization             uint64 `json:"tiler_utilization" mapstructure:"Tiler Utilization %"`
		XRVideoCardRunTimeStamp      uint64 `json:"xr_video_card_run_time_stamp" mapstructure:"XRVideoCardRunTimeStamp"`
		RecoveryCount                uint64 `json:"recovery_count" mapstructure:"recoveryCount"`
	}
	GraphicsData struct {
		Timestamp time.Time `json:"timestamp" mapstructure:"-"`
		FPS       float64   `json:"fps" mapstructure:"CoreAnimationFramesPerSecond"`
	}
)

func (s *GraphicsService) handleMessage(msg *dtx.Message) (*GraphicsData, error) {
	if msg == nil {
		return nil, fmt.Errorf("received null message")
	} else if len(msg.Payload) == 0 {
		return nil, fmt.Errorf("received empty payload")
	}

	payload, ok := msg.Payload[0].(map[string]any)
	if !ok {
		return nil, fmt.Errorf("received invalid payload type, expected: map[string]any, but got: %T", msg.Payload[0])
	}

	data := &GraphicsData{
		Timestamp: time.Now(),
	}
	if err := mapstructure.WeakDecode(payload, data); err != nil {
		return nil, fmt.Errorf("failed to decode GraphicsData: %w", err)
	}

	return data, nil
}
