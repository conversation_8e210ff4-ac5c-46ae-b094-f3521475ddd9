package instruments

import (
	"fmt"
	"log/slog"
	"reflect"
	"slices"
	"sync/atomic"
	"time"

	"github.com/go-viper/mapstructure/v2"

	"github.com/danielpaulus/go-ios/ios"
	dtx "github.com/danielpaulus/go-ios/ios/dtx_codec"
	"github.com/danielpaulus/go-ios/ios/installationproxy"
)

const (
	serviceNameOfSysmontap = "com.apple.instruments.server.services.sysmontap"

	selectorOfSetConfig = "setConfig:"
	selectorOfStart     = "start"
	selectorOfStop      = "stop"

	DefaultOutputInterval = time.Second
	MinOutputInterval     = 10 * time.Millisecond
	MaxOutputInterval     = 60 * time.Second

	payloadKeyOfProcessesAttributes = "ProcessesAttributes"
	payloadKeyOfSystemAttributes    = "SystemAttributes"
	payloadKeyOfProcesses           = "Processes"
	payloadKeyOfSystem              = "System"
	payloadKeyOfName                = "name"
	payloadKeyOfCPUUsage            = "cpuUsage"

	typeFullNameOfNSNull = "nskeyedarchiver.NSNull"
)

var _ dtx.Dispatcher = (*sysmontapMsgDispatcher)(nil)

type sysmontapMsgDispatcher struct {
	messageChan chan *dtx.Message
	closed      *atomic.Bool
}

func newSysmontapMsgDispatcher() *sysmontapMsgDispatcher {
	return &sysmontapMsgDispatcher{
		messageChan: make(chan *dtx.Message),
		closed:      new(atomic.Bool),
	}
}

func (d *sysmontapMsgDispatcher) Close() {
	defer func() {
		for range d.messageChan {
		}
	}()

	d.closed.Store(true)
	close(d.messageChan)
}

func (d *sysmontapMsgDispatcher) Dispatch(m *dtx.Message) {
	if !d.closed.Load() {
		d.messageChan <- m
	}
}

type (
	SysmontapOption       func(*SysmontapService)
	SysmontapCallbackFunc func(*SysmontapData)

	SysmontapService struct {
		dispatcher *sysmontapMsgDispatcher
		conn       *dtx.Connection
		channel    *dtx.Channel

		deviceInfoService *DeviceInfoService
		closeChan         chan struct{}

		bundleID            string
		executable          string
		interval            time.Duration
		processesAttributes []string
		systemAttributes    []string
		callbackFunc        SysmontapCallbackFunc
	}
)

func WithBundleIDOfSysmontap(bundleID string) SysmontapOption {
	return func(s *SysmontapService) {
		s.bundleID = bundleID
	}
}

func WithIntervalOfSysmontap(interval time.Duration) SysmontapOption {
	return func(s *SysmontapService) {
		if interval < MinOutputInterval {
			s.interval = MinOutputInterval
		} else if interval > MaxOutputInterval {
			s.interval = MaxOutputInterval
		} else {
			s.interval = interval
		}
	}
}

func WithCallbackFuncOfSysmontap(fn SysmontapCallbackFunc) SysmontapOption {
	return func(s *SysmontapService) {
		s.callbackFunc = fn
	}
}

// NewSysmontapService creates a SysmontapService
func NewSysmontapService(device ios.DeviceEntry, opts ...SysmontapOption) (*SysmontapService, error) {
	dispatcher := newSysmontapMsgDispatcher()
	conn, err := connectInstrumentsWithMsgDispatcher(device, dispatcher)
	if err != nil {
		return nil, err
	}
	defer func() {
		if err != nil {
			if conn != nil {
				_ = conn.Close()
			}
			if dispatcher != nil {
				dispatcher.Close()
			}
		}
	}()

	deviceInfoService, err := NewDeviceInfoService(device)
	if err != nil {
		return nil, err
	}
	defer func() {
		if err != nil && deviceInfoService != nil {
			deviceInfoService.Close()
		}
	}()

	channel := conn.RequestChannelIdentifier(serviceNameOfSysmontap, loggingDispatcher{conn})
	s := &SysmontapService{
		dispatcher: dispatcher,
		conn:       conn,
		channel:    channel,

		deviceInfoService: deviceInfoService,
		closeChan:         make(chan struct{}),

		interval:            DefaultOutputInterval,
		processesAttributes: []string{},
		systemAttributes:    []string{},
		callbackFunc:        sysmontapDataLog,
	}
	for _, opt := range opts {
		opt(s)
	}
	if s.bundleID != "" {
		s.executable, err = getAppExecutableByBundleID(device, s.bundleID)
		if err != nil {
			return nil, err
		}
	}
	// ["__arch", "__restricted", "__sandbox", "__suddenTerm", "appSleep", "avgPowerScore", "coalitionID", "comm", "cowFaults", "cpuTotalSystem", "cpuTotalUser", "cpuUsage", "ctxSwitch", "diskBytesRead", "diskBytesWritten", "faults", "gid", "intWakeups", "latencyQosTier", "machPortCount", "memAnon", "memAnonPeak", "memCompressed", "memPurgeable", "memRPrvt", "memRShrd", "memResidentSize", "memVirtualSize", "msgRecv", "msgSent", "name", "nfiles", "nice", "numRunning", "parentUniqueID", "pgid", "physFootprint", "pid", "pjobc", "platIdleWakeups", "policy", "powerScore", "ppid", "priority", "procAge", "procFlags", "procStatus", "procXstatus", "responsiblePID", "responsibleUniqueID", "rgid", "ruid", "startAbsTime", "svgid", "svuid", "sysCallsMach", "sysCallsUnix", "tdev", "threadCount", "threadsSystem", "threadsUser", "throughputQosTier", "timerWakeBin1", "timerWakeBin2", "totalEnergyScore", "tpgid", "uid", "uniqueID", "vmPageIns", "wiredSize", "wqBlockedThreads", "wqNumThreads", "wqRunThreads", "wqState"]
	if s.processesAttributes, err = s.deviceInfoService.ProcessAttributes(); err != nil {
		return nil, err
	}
	// ["__vmSwapUsage", "diskBytesRead", "diskBytesWritten", "diskReadOps", "diskWriteOps", "netBytesIn", "netBytesOut", "netPacketsIn", "netPacketsOut", "physMemSize", "threadCount", "vmActiveCount", "vmCompressions", "vmCompressorPageCount", "vmCowFaults", "vmDecompressions", "vmExtPageCount", "vmFaults", "vmFreeCount", "vmHits", "vmInactiveCount", "vmIntPageCount", "vmLookups", "vmPageIns", "vmPageOuts", "vmPurgeableCount", "vmPurges", "vmReactivations", "vmSize", "vmSpeculativeCount", "vmSwapIns", "vmSwapOuts", "vmThrottledCount", "vmTotalUncompPagesInComp", "vmUsedCount", "vmWireCount", "vmZeroFillCount"]
	if s.systemAttributes, err = s.deviceInfoService.SystemAttributes(); err != nil {
		return nil, err
	}

	config := map[string]any{
		"ur":             s.interval.Milliseconds(), // ms
		"bm":             0,
		"procAttrs":      s.processesAttributes,
		"sysAttrs":       s.systemAttributes,
		"cpuUsage":       true,
		"physFootprint":  true,
		"sampleInterval": s.interval.Nanoseconds(), // ns
	}
	_, err = s.channel.MethodCall(selectorOfSetConfig, config)
	if err != nil {
		return nil, err
	}

	err = s.channel.MethodCallAsync(selectorOfStart)
	if err != nil {
		return nil, err
	}

	go s.receiveMessage()
	return s, nil
}

func getAppExecutableByBundleID(device ios.DeviceEntry, bundleID string) (string, error) {
	svc, err := installationproxy.New(device)
	if err != nil {
		return "", err
	}
	defer svc.Close()

	apps, err := svc.BrowseAllApps()
	if err != nil {
		return "", err
	}

	for _, app := range apps {
		if app.CFBundleIdentifier == bundleID {
			return app.CFBundleExecutable, nil
		}
	}

	return "", fmt.Errorf("not found app, udid: %s, bundle_id: %s", device.Properties.SerialNumber, bundleID)
}

func sysmontapDataLog(data *SysmontapData) {
	if data == nil {
		slog.Warn("got a null sysmontap data")
	} else {
		slog.Info("got a sysmontap data", "data", data)
	}
}

// Close closes up the DTX connection, message dispatcher and dtx.Message channel
func (s *SysmontapService) Close() error {
	if s == nil {
		return nil
	}

	if s.channel != nil {
		_ = s.channel.MethodCallAsync(selectorOfStop)
	}

	if s.deviceInfoService != nil {
		s.deviceInfoService.Close()
	}

	if s.dispatcher != nil {
		s.dispatcher.Close()
	}
	close(s.closeChan)

	if s.conn != nil {
		return s.conn.Close()
	}

	return nil
}

func (s *SysmontapService) receiveMessage() {
	for {
		select {
		case <-s.closeChan:
			return
		case msg, ok := <-s.dispatcher.messageChan:
			if !ok {
				slog.Info("the sysmontap message channel is closed")
				return
			}

			data, err := s.handleMessage(msg)
			if err != nil {
				slog.Debug("ignore the message", "error", err)
				continue
			}

			if s.callbackFunc != nil {
				s.callbackFunc(data)
			}
		}
	}
}

type (
	sysmontapPayload1 struct {
		Type                uint64     `json:"type" mapstructure:"Type"`
		CPUCount            uint64     `json:"cpu_count" mapstructure:"CPUCount"`
		EnabledCPUs         uint64     `json:"enabled_cpus" mapstructure:"EnabledCPUs"`
		PerCPUUsage         []CPUUsage `json:"per_cpu_usage" mapstructure:"PerCPUUsage"`
		SystemCPUUsage      CPUUsage   `json:"system_cpu_usage" mapstructure:"SystemCPUUsage"`
		ProcessesAttributes []string   `json:"processes_attributes" mapstructure:"ProcessesAttributes"`
		SystemAttributes    []string   `json:"system_attributes" mapstructure:"SystemAttributes"`
		System              []any      `json:"system" mapstructure:"System"`
		StartMachAbsTime    uint64     `json:"start_mach_abs_time" mapstructure:"StartMachAbsTime"`
		EndMachAbsTime      uint64     `json:"end_mach_abs_time" mapstructure:"EndMachAbsTime"`
	}
	sysmontapPayload2 struct {
		Type             uint64           `json:"type" mapstructure:"Type"`
		Processes        map[string][]any `json:"processes" mapstructure:"Processes"`
		StartMachAbsTime uint64           `json:"start_mach_abs_time" mapstructure:"StartMachAbsTime"`
		EndMachAbsTime   uint64           `json:"end_mach_abs_time" mapstructure:"EndMachAbsTime"`
	}
	CPUUsage struct {
		CPUNiceLoad   float64 `json:"cpu_nice_load" mapstructure:"CPU_NiceLoad"`
		CPUSystemLoad float64 `json:"cpu_system_load" mapstructure:"CPU_SystemLoad"`
		CPUTotalLoad  float64 `json:"cpu_total_load" mapstructure:"CPU_TotalLoad"`
		CPUUserLoad   float64 `json:"cpu_user_load" mapstructure:"CPU_UserLoad"`
	}
	SysmontapData struct {
		Timestamp time.Time `json:"timestamp" mapstructure:"-"`
		Usage     float64   `json:"usage" mapstructure:"cpuUsage"`
		PSS       int64     `json:"pss" mapstructure:"physFootprint"`
		RSS       int64     `json:"rss" mapstructure:"memResidentSize"`
		VSS       int64     `json:"vss" mapstructure:"memVirtualSize"`

		PID  int64  `json:"pid" mapstructure:"pid"`
		Name string `json:"name" mapstructure:"name"`
	}
)

func (s *SysmontapService) handleMessage(msg *dtx.Message) (*SysmontapData, error) {
	if msg == nil {
		return nil, fmt.Errorf("received null message")
	} else if len(msg.Payload) == 0 {
		return nil, fmt.Errorf("received empty payload")
	}

	payload, ok := msg.Payload[0].([]any)
	if !ok {
		return nil, fmt.Errorf("received invalid payload type, expected: []any, but got: %T", msg.Payload[0])
	} else if len(payload) != 2 {
		return nil, fmt.Errorf("received invalid payload length, expected: 2, but got: %d", len(payload))
	}

	if s.executable == "" {
		// get device sysmontap data
		return s.getDeviceSysmontapDataFromPayload(payload)
	} else {
		// get process sysmontap data
		return s.getProcessSysmontapDataFromPayload(payload)
	}
}

func (s *SysmontapService) getDeviceSysmontapDataFromPayload(payload []any) (*SysmontapData, error) {
	return nil, fmt.Errorf("not supported yet")
}

func (s *SysmontapService) getProcessSysmontapDataFromPayload(payload []any) (*SysmontapData, error) {
	if len(payload) != 2 {
		return nil, fmt.Errorf("received invalid payload length, expected: 2, but got: %d", len(payload))
	}

	m1, ok1 := payload[0].(map[string]any)
	m2, ok2 := payload[1].(map[string]any)
	if !ok1 || !ok2 {
		return nil, fmt.Errorf(
			"received invalid payload type, expected: map[string]any, but got: %T, %T", payload[0], payload[1],
		)
	}

	var (
		p1 sysmontapPayload1
		p2 sysmontapPayload2
	)
	if err := mapstructure.Decode(m1, &p1); err != nil {
		return nil, fmt.Errorf("failed to decode payload1: %w", err)
	}
	if err := mapstructure.Decode(m2, &p2); err != nil {
		return nil, fmt.Errorf("failed to decode payload2: %w", err)
	}

	processesAttributes := p1.ProcessesAttributes
	if len(processesAttributes) == 0 {
		processesAttributes = s.processesAttributes
	}
	processesAttributesLen := len(processesAttributes)

	attrs := make(map[string]any, processesAttributesLen)
	indexOfName := slices.Index(processesAttributes, payloadKeyOfName)
	for _, process := range p2.Processes {
		if len(process) != processesAttributesLen {
			continue
		} else if process[indexOfName] != s.executable {
			continue
		}

		for i, attr := range processesAttributes {
			attrs[attr] = process[i]
		}
		break
	}
	if len(attrs) == 0 {
		return nil, fmt.Errorf("not found process sysmontap data, name: %s", s.executable)
	}

	data := &SysmontapData{
		Timestamp: time.Now(),
		Name:      s.executable,
	}
	decoder, err := mapstructure.NewDecoder(
		&mapstructure.DecoderConfig{
			DecodeHook:       toCPUUsageHookFunc,
			WeaklyTypedInput: true,
			Result:           data,
		},
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create mapstructure decoder: %w", err)
	}
	if err = decoder.Decode(attrs); err != nil {
		return nil, fmt.Errorf("failed to decode SysmontapData: %w", err)
	}

	return data, nil
}

func toCPUUsageHookFunc(from, to reflect.Type, data any) (any, error) {
	if from.String() == typeFullNameOfNSNull && to.Kind() == reflect.Float64 {
		return 0.0, nil
	}

	return data, nil
}
